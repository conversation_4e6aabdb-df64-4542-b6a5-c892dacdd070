const API_BASE_URL = '/.netlify/functions';

class ApiClient {
  async request(action, params = {}) {
    const url = `${API_BASE_URL}/getUserData`;
    const config = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ action, params }),
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  async trackUser(path) {
    return this.request('trackUser', { path });
  }

  async checkUserStatus() {
    return this.request('checkUserStatus', {});
  }

  async checkAdminStatus() {
    return this.request('checkAdminStatus', {});
  }

  async adminLogin(accessKey) {
    return this.request('adminLogin', { accessKey });
  }

  async test() {
    return this.request('test');
  }

  async getMyHwid() {
    return this.request('getMyHwid');
  }

  async getAdminKeys() {
    return this.request('getAdminKeys', {});
  }

  async getScriptRequests() {
    return this.request('getScriptRequests', {});
  }

  async updateRequestStatus(requestId, status) {
    return this.request('updateRequestStatus', { requestId, status });
  }

  async createScript(scriptData) {
    return this.request('createScript', { scriptData });
  }

  async updateScript(scriptId, scriptData) {
    return this.request('updateScript', { scriptId, scriptData });
  }

  async deleteScript(scriptId) {
    return this.request('deleteScript', { scriptId });
  }

  async getScripts() {
    return this.request('getScripts');
  }

  async rateScript(scriptId, rating) {
    return this.request('rateScript', { scriptId, rating });
  }

  async getUserRatings() {
    return this.request('getUserRatings', {});
  }

  async getAdminStats() {
    return this.request('getAdminStats', {});
  }

  async submitScriptRequest(gameName, gameLink, scriptDescription, discordUsername) {
    return this.request('submitScriptRequest', {
      gameName,
      gameLink,
      scriptDescription,
      discordUsername
    });
  }
}

export const apiClient = new ApiClient();
