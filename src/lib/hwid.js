const HWID_STORAGE_KEY = '6footscripts_hwid';

// SHA-256 hash function
async function sha256(message) {
  const msgBuffer = new TextEncoder().encode(message);
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
}

// Get client IP address from a service
async function getClientIP() {
  try {
    // Try multiple IP services for reliability
    const services = [
      'https://api.ipify.org?format=json',
      'https://ipapi.co/json/',
      'https://httpbin.org/ip'
    ];

    for (const service of services) {
      try {
        const response = await fetch(service);
        const data = await response.json();

        // Different services return IP in different formats
        const ip = data.ip || data.query || data.origin;
        if (ip) {
          return ip;
        }
      } catch (error) {
        console.warn(`Failed to get IP from ${service}:`, error);
        continue;
      }
    }

    // Fallback IP if all services fail
    return '127.0.0.1';
  } catch (error) {
    console.warn('All IP services failed, using fallback:', error);
    return '127.0.0.1';
  }
}

export const getHwid = async () => {
  let hwid = localStorage.getItem(HWID_STORAGE_KEY);
  if (!hwid) {
    try {
      // Get the user's IP address
      const ip = await getClientIP();
      // Hash the IP to create a non-reversible identifier
      // Users with the same IP will get the same HWID
      hwid = await sha256(ip);
      localStorage.setItem(HWID_STORAGE_KEY, hwid);
    } catch (error) {
      console.error('Failed to generate HWID:', error);
      // Fallback to hashing a default IP
      hwid = await sha256('127.0.0.1');
      localStorage.setItem(HWID_STORAGE_KEY, hwid);
    }
  }
  return hwid;
};