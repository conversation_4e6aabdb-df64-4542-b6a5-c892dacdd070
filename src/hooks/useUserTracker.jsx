import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';

export const useUserTracker = () => {
    const [isBlacklisted, setIsBlacklisted] = useState(false);
    const [loading, setLoading] = useState(true);
    const location = useLocation();

    useEffect(() => {
        const trackUser = async () => {
            setLoading(true);
            try {
                const statusResult = await apiClient.checkUserStatus();
                console.log('Blacklist check result:', statusResult);
                setIsBlacklisted(statusResult.isBlacklisted);

                if (!location.pathname.startsWith('/admin')) {
                    await apiClient.trackUser(location.pathname);
                }
            } catch (error) {
                console.error("An error occurred during user tracking:", error);
            } finally {
                setLoading(false);
            }
        };

        trackUser();
    }, [location.pathname]);

    return { isBlacklisted, loading };
};