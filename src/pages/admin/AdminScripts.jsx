
import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, PlusCircle, Edit, Trash2 } from 'lucide-react';
import { Dialog, DialogTrigger, DialogContent } from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/components/ui/use-toast';
import ScriptForm from './ScriptForm';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';

const AdminScripts = () => {
  const [scripts, setScripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingScript, setEditingScript] = useState(null);
  const { toast } = useToast();
  const { getAuthHeaders } = useAdminAuth();

  const fetchScripts = useCallback(async () => {
    setLoading(true);
    try {
      const result = await apiClient.getScripts();
      setScripts(result.data || []);
    } catch (error) {
      console.error('Error fetching scripts:', error);
      toast({ variant: 'destructive', title: 'Error', description: 'Failed to load scripts.' });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchScripts();
  }, [fetchScripts]);

  const handleEdit = (script) => {
    setEditingScript(script);
    setIsEditorOpen(true);
  };

  const handleAdd = () => {
    setEditingScript(null);
    setIsEditorOpen(true);
  };

  const handleSave = () => {
    setIsEditorOpen(false);
    fetchScripts();
  };

  const handleDelete = async (scriptId) => {
    try {
      const hwid = getHwid();
      await apiClient.deleteScript(hwid, scriptId);
      toast({
        title: "Script Deleted!",
        description: "Script has been deleted successfully."
      });
      fetchScripts(); 
    } catch (error) {
      console.error('Error deleting script:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete script.'
      });
    }
  };


  return (
    <>
      <Helmet>
        <title>Script Manager - 6FootScripts Admin</title>
      </Helmet>
      <PageWrapper title="Script Manager" description="Add, edit, and manage all public scripts.">
        <Dialog open={isEditorOpen} onOpenChange={setIsEditorOpen}>
            <Card>
            <CardHeader>
                <div className="flex justify-between items-center">
                <div>
                    <CardTitle>All Scripts</CardTitle>
                    <CardDescription>A list of all scripts in the database.</CardDescription>
                </div>
                <DialogTrigger asChild>
                    <Button onClick={handleAdd}>
                        <PlusCircle className="w-4 h-4 mr-2" />
                        Add New Script
                    </Button>
                </DialogTrigger>
                </div>
            </CardHeader>
            <CardContent>
                {loading ? (
                <div className="flex justify-center py-8"><Loader2 className="animate-spin h-8 w-8 text-primary" /></div>
                ) : (
                <div className="rounded-md border">
                    <div className="max-h-[70vh] overflow-y-auto">
                    {scripts.map(script => (
                        <div key={script.id} className="flex justify-between items-center p-3 border-b last:border-b-0">
                        <div>
                            <p className="font-semibold">{script.title}</p>
                            <p className="text-sm text-muted-foreground">{script.status} - Last Updated: {new Date(script.last_updated).toLocaleDateString()}</p>
                        </div>
                        <div className="flex items-center gap-2">
                            <DialogTrigger asChild>
                                <Button variant="outline" size="icon" onClick={() => handleEdit(script)}><Edit className="w-4 h-4" /></Button>
                            </DialogTrigger>
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <Button variant="destructive" size="icon"><Trash2 className="w-4 h-4"/></Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>Are you sure you want to delete this script?</AlertDialogTitle>
                                        <AlertDialogDescription>This action cannot be undone. This will permanently delete the "{script.title}" script.</AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction onClick={() => handleDelete(script.id)}>Delete</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </div>
                        </div>
                    ))}
                    </div>
                </div>
                )}
            </CardContent>
            </Card>
            <DialogContent className="sm:max-w-[625px]">
                <ScriptForm script={editingScript} onSave={handleSave} onCancel={() => setIsEditorOpen(false)} />
            </DialogContent>
        </Dialog>
      </PageWrapper>
    </>
  );
};

export default AdminScripts;
