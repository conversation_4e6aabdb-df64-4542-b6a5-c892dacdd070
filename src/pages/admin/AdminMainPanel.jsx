
import { useState, useEffect, useMemo, useRef } from 'react';
import { Helmet } from 'react-helmet';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Sector } from 'recharts';
import { Loader2, Users, FileText, KeyRound, Code, Calendar, BarChart2, <PERSON><PERSON><PERSON> as PieChartIcon } from 'lucide-react';
import PageWrapper from '@/components/PageWrapper';
import { useToast } from '@/components/ui/use-toast';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion, animate } from 'framer-motion';

const AnimatedStat = ({ value }) => {
  const ref = useRef(null);

  useEffect(() => {
    const node = ref.current;
    if (!node) return;

    const controls = animate(0, value, {
      duration: 1.5,
      ease: "easeOut",
      onUpdate(latest) {
        node.textContent = Math.round(latest).toLocaleString();
      }
    });

    return () => controls.stop();
  }, [value]);

  return <span ref={ref} />;
};

const CustomAreaTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.2 }}
        className="p-3 bg-card/80 backdrop-blur-xl border border-white/10 rounded-lg shadow-2xl shadow-primary/20"
      >
        <p className="label font-bold text-lg text-foreground">{label}</p>
        <p className="intro text-primary font-semibold">{`Requests: ${payload[0].value}`}</p>
      </motion.div>
    );
  }
  return null;
};

const renderActiveShape = (props) => {
  const RADIAN = Math.PI / 180;
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
  const sin = Math.sin(-RADIAN * midAngle);
  const cos = Math.cos(-RADIAN * midAngle);

  const textRadius = outerRadius + 40;
  const textX = cx + textRadius * cos;
  const textY = cy + textRadius * sin;
  const textAnchor = cos >= 0 ? 'start' : 'end';

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
        className="drop-shadow-[0_4px_8px_rgba(0,0,0,0.4)]"
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 3}
        outerRadius={outerRadius + 8}
        fill={fill}
      />
      {/* Text positioned well outside i hope */}
      <text
        x={textX}
        y={textY}
        textAnchor={textAnchor}
        fill="hsl(var(--foreground))"
        className="text-sm font-bold"
        dominantBaseline="middle"
      >
        {payload.name}
      </text>
      <text
        x={textX}
        y={textY + 16}
        textAnchor={textAnchor}
        fill="hsl(var(--muted-foreground))"
        className="text-xs"
        dominantBaseline="middle"
      >
        {`${value} (${(percent * 100).toFixed(1)}%)`}
      </text>
    </g>
  );
};

const STATUS_COLORS = {
  Pending: 'hsl(48, 96%, 57%)',
  Accepted: 'hsl(142, 71%, 45%)',
  Denied: 'hsl(0, 84%, 60%)',
  'In Progress': 'hsl(var(--primary))',
  Completed: 'hsl(142, 71%, 45%)',
  Rejected: 'hsl(0, 84%, 60%)',
  'No Data': 'hsl(var(--muted))',
};

const AdminMainPanel = () => {
  const [stats, setStats] = useState({ requests: 0, keys: 0, visits: 0, scripts: 0 });
  const [allRequests, setAllRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [activePieIndex, setActivePieIndex] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const hwid = getHwid();

        const [requestsResult, keysResult] = await Promise.all([
          apiClient.getScriptRequests(hwid),
          apiClient.getAdminKeys(hwid)
        ]);

        const requests = requestsResult.data || [];
        const keys = keysResult.data || [];
        setAllRequests(requests);

        setStats({
          requests: requests.length,
          keys: keys.length,
          visits: 0, // Will be 0 for now since we don't have a visits API
          scripts: 0, // Will be 0 for now since we don't have a scripts count API
        });

      } catch (error) {
        console.error('Failed to fetch admin data:', error);
        toast({
          variant: 'destructive',
          title: 'Failed to Load Data',
          description: 'Could not load admin data. Please try again.'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const { availableYears, requestsByMonth, requestsByStatus } = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const allYears = [];
    for (let year = currentYear; year >= 2020; year--) {
      allYears.push(year);
    }
    const filteredRequests = allRequests.filter(r => new Date(r.created_at).getFullYear() === selectedYear);

    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      name: new Date(0, i).toLocaleString('default', { month: 'short' }),
      requests: 0,
    }));
    filteredRequests.forEach(req => {
      const month = new Date(req.created_at).getMonth();
      monthlyData[month].requests++;
    });

    const statusData = Object.entries(filteredRequests.reduce((acc, req) => {
      const status = req.status || 'Pending';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {})).map(([name, value]) => ({ name, value }));

    return { availableYears: allYears, requestsByMonth: monthlyData, requestsByStatus: statusData };
  }, [allRequests, selectedYear]);

  const onPieEnter = (_, index) => setActivePieIndex(index);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="w-16 h-16 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Admin Home - 6FootScripts</title>
      </Helmet>
      <PageWrapper title="Dashboard" description={`Welcome back! Here's what's happening with your project.`} titleAlignment="left">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[
            { title: 'Total Requests', data: stats.requests, icon: FileText },
            { title: 'Admin Keys', data: stats.keys, icon: KeyRound },
            { title: 'Unique Visitors', data: stats.visits, icon: Users },
          ].map((item, index) => (
            <motion.div key={item.title} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: index * 0.1 }}>
              <Card className="bg-card/70 backdrop-blur-sm border-white/5 hover:border-primary/30 transition-all duration-300 shadow-lg shadow-black/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">{item.title}</CardTitle>
                  <item.icon className="h-5 w-5 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold text-primary"><AnimatedStat value={item.data} /></div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.3 }} className="mt-10">
          <h3 className="text-2xl font-bold tracking-tight mb-4">Management Sections</h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              { to: "/admin/keys", icon: KeyRound, title: "Key Manager" },
              { to: "/admin/requests", icon: FileText, title: "Request Manager" },
              { to: "/admin/scripts", icon: Code, title: "Script Manager" },
              { to: "/admin/users", icon: Users, title: "User Manager" }
            ].map((item, index) => (
              <Link key={index} to={item.to} className="hover:no-underline group">
                <Card className="bg-card/70 backdrop-blur-sm border-white/5 hover:border-primary/30 transition-all duration-300 h-full flex flex-col justify-center items-center text-center p-6 hover:shadow-primary/20 hover:shadow-2xl hover:-translate-y-2">
                  <item.icon className="h-10 w-10 text-primary mb-3 transition-transform duration-300 group-hover:scale-110" />
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                </Card>
              </Link>
            ))}
          </div>
        </motion.div>

        <div className="mt-10 grid gap-8 md:grid-cols-1 lg:grid-cols-5">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.4 }} className="lg:col-span-3">
            <Card className="bg-card/70 backdrop-blur-sm border-white/5 shadow-lg shadow-black/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-lg"><BarChart2 className="text-primary" /> Script Requests by Month</CardTitle>
                  </div>
                  <Select onValueChange={(value) => setSelectedYear(Number(value))} defaultValue={selectedYear}>
                    <SelectTrigger className="w-[120px] bg-background/50 border-white/10">
                      <Calendar className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>{availableYears.map(year => <SelectItem key={year} value={year}>{year}</SelectItem>)}</SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={requestsByMonth} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <defs>
                        <linearGradient id="colorRequests" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.8}/>
                          <stop offset="50%" stopColor="#ec4899" stopOpacity={0.6}/>
                          <stop offset="95%" stopColor="#f97316" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                      <XAxis
                        dataKey="name"
                        stroke="hsl(var(--muted-foreground))"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        allowDecimals={false}
                        stroke="hsl(var(--muted-foreground))"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip content={<CustomAreaTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="requests"
                        stroke="hsl(var(--primary))"
                        strokeWidth={3}
                        fill="url(#colorRequests)"
                        dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.5 }} className="lg:col-span-2">
            <Card className="bg-card/70 backdrop-blur-sm border-white/5 shadow-lg shadow-black/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg"><PieChartIcon className="text-primary" /> Script Requests by Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[350px] p-2">
                  {requestsByStatus.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart margin={{ top: 40, right: 80, bottom: 40, left: 80 }}>
                        <Pie
                          activeIndex={activePieIndex}
                          activeShape={renderActiveShape}
                          data={requestsByStatus}
                          cx="50%"
                          cy="50%"
                          innerRadius={45}
                          outerRadius={85}
                          fill="hsl(var(--primary))"
                          dataKey="value"
                          onMouseEnter={onPieEnter}
                          stroke="hsl(var(--background))"
                          strokeWidth={2}
                        >
                          {requestsByStatus.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.name]} />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-muted-foreground gap-2">
                      <PieChartIcon className="w-10 h-10" />
                      <span className="font-medium">No request data for {selectedYear}.</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageWrapper>
    </>
  );
};

export default AdminMainPanel;
