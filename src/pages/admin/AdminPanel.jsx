import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Info, Hourglass, CheckCircle2, XCircle, Database, Loader2, KeyRound, Copy } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabaseClient';
import { useToast } from '@/components/ui/use-toast';
import { v4 as uuidv4 } from 'uuid';

const KeyManager = () => {
    const [keys, setKeys] = useState([]);
    const [newKey, setNewKey] = useState('');
    const [loadingKeys, setLoadingKeys] = useState(true);
    const [isGenerating, setIsGenerating] = useState(false);
    const { toast } = useToast();

    const fetchKeys = useCallback(async () => {
        setLoadingKeys(true);
        const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });
        if (error) {
            toast({ title: 'Error fetching keys', description: error.message, variant: 'destructive' });
        } else {
            setKeys(data);
        }
        setLoadingKeys(false);
    }, [toast]);

    useEffect(() => {
        fetchKeys();
    }, [fetchKeys]);

    const generateNewKey = async () => {
        setIsGenerating(true);
        setNewKey('');
        const generatedKey = uuidv4().toUpperCase();
        const { error } = await supabase.from('admin_keys').insert([{ access_key: generatedKey }]);

        if (error) {
            toast({ variant: 'destructive', title: 'Error', description: `Could not generate key: ${error.message}` });
        } else {
            setNewKey(generatedKey);
            toast({ title: 'Success!', description: 'New key generated and ready to be shared.' });
            fetchKeys();
        }
        setIsGenerating(false);
    };
    
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
        toast({ title: 'Copied!', description: 'Key copied to clipboard.' });
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Access Key Manager</CardTitle>
                <CardDescription>Generate and manage admin access keys.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <Button onClick={generateNewKey} disabled={isGenerating}>
                    {isGenerating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <KeyRound className="mr-2 h-4 w-4" />}
                    Generate New Key
                </Button>
                {newKey && (
                    <Alert className="mt-4">
                        <KeyRound className="h-4 w-4" />
                        <AlertTitle>New Key Generated!</AlertTitle>
                        <AlertDescription>Share this key with a new admin. It can only be used once to bind to a device.</AlertDescription>
                        <div className="flex w-full items-center space-x-2 mt-2">
                            <Input readOnly value={newKey} />
                            <Button variant="outline" size="icon" onClick={() => copyToClipboard(newKey)}>
                                <Copy className="h-4 w-4" />
                            </Button>
                        </div>
                    </Alert>
                )}
                 <div className="space-y-2">
                    <h4 className="font-semibold">Existing Keys</h4>
                    {loadingKeys ? <Loader2 className="animate-spin" /> : (
                        <div className="rounded-md border max-h-60 overflow-y-auto">
                            {keys.map(key => (
                                <div key={key.id} className="flex justify-between items-center p-2 border-b last:border-b-0">
                                    <span className="font-mono text-xs truncate mr-4" title={key.access_key}>{key.access_key}</span>
                                    {key.hwid ? 
                                        <span className="text-xs text-green-500 font-semibold">Assigned</span> : 
                                        <span className="text-xs text-yellow-500 font-semibold">Unassigned</span>
                                    }
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    )
}


const AdminPanel = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchRequests = useCallback(async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from('script_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      toast({
        title: "Error fetching requests",
        description: error.message,
        variant: "destructive"
      });
      setRequests([]);
    } else {
      setRequests(data);
    }
    setLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  const handleStatusChange = async (id, newStatus) => {
    const originalRequests = [...requests];
    const updatedRequests = requests.map(req => 
      req.id === id ? { ...req, status: newStatus } : req
    );
    setRequests(updatedRequests);

    const { error } = await supabase
      .from('script_requests')
      .update({ status: newStatus })
      .eq('id', id);

    if (error) {
      toast({
        title: "Update Failed",
        description: `Could not update status: ${error.message}`,
        variant: "destructive"
      });
      setRequests(originalRequests);
    } else {
      toast({
        title: "Status Updated!",
        description: `Request has been marked as ${newStatus}.`
      });
    }
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 'Accepted':
        return {
          icon: <CheckCircle2 className="w-5 h-5 text-green-500" />,
          borderColor: 'border-l-green-500',
          textColor: 'text-green-500',
        };
      case 'Denied':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          borderColor: 'border-l-red-500',
          textColor: 'text-red-500',
        };
      default:
        return {
          icon: <Hourglass className="w-5 h-5 text-yellow-500" />,
          borderColor: 'border-l-yellow-500',
          textColor: 'text-yellow-500',
        };
    }
  };

  return (
    <>
      <Helmet>
        <title>Admin Panel - 6FootScripts</title>
        <meta name="description" content="Manage script requests and admin access." />
      </Helmet>
      <PageWrapper
        title={<>Admin Panel: <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Dashboard</span></>}
        description="Manage script requests and administrator access keys."
      >
        <div className="grid gap-8 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <h3 className="text-2xl font-bold tracking-tight">Script Requests</h3>
            {loading ? (
              <div className="text-center py-16">
                <Loader2 className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4 animate-spin" />
                <h3 className="text-2xl font-bold mb-2">Loading Requests...</h3>
                <p className="text-muted-foreground">Fetching the latest data from Supabase.</p>
              </div>
            ) : requests.length === 0 ? (
              <div className="text-center py-16 border rounded-lg">
                <Info className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-2">No Requests Yet</h3>
                <p className="text-muted-foreground">Looks like the request queue is empty.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {requests.map((request, index) => {
                  const statusInfo = getStatusInfo(request.status);
                  return (
                    <motion.div
                      key={request.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <Card className={`overflow-hidden border-l-4 ${statusInfo.borderColor}`}>
                        <CardHeader>
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle>{request.game_name}</CardTitle>
                              <CardDescription>Requested on {new Date(request.created_at).toLocaleDateString()}</CardDescription>
                            </div>
                            <div className={`flex items-center gap-2 font-semibold ${statusInfo.textColor}`}>
                              {statusInfo.icon}
                              <span>{request.status}</span>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground">{request.script_description}</p>
                          {request.discord_username && (
                             <p className="text-xs text-muted-foreground mt-4">Requester: {request.discord_username}</p>
                          )}
                        </CardContent>
                        <CardFooter className="bg-card-footer p-2 flex justify-end gap-2 border-t">
                            <Button size="sm" variant="ghost" className="text-green-500 hover:bg-green-500/10 hover:text-green-600" onClick={() => handleStatusChange(request.id, 'Accepted')}>Accept</Button>
                            <Button size="sm" variant="ghost" className="text-red-500 hover:bg-red-500/10 hover:text-red-600" onClick={() => handleStatusChange(request.id, 'Denied')}>Deny</Button>
                            <Button size="sm" variant="ghost" className="text-yellow-500 hover:bg-yellow-500/10 hover:text-yellow-600" onClick={() => handleStatusChange(request.id, 'Pending')}>Pend</Button>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
          <div className="space-y-6">
             <h3 className="text-2xl font-bold tracking-tight">System</h3>
            <KeyManager />
            <Alert className="bg-green-900/10 border-green-500/30 text-green-foreground">
              <Database className="h-4 w-4 !text-green-500" />
              <AlertTitle className="text-green-400">Database Connected</AlertTitle>
              <AlertDescription className="text-green-400/80">
                You are connected to the Supabase database.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </PageWrapper>
    </>
  );
};

export default AdminPanel;