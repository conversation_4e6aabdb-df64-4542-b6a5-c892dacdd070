
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, UserX, Edit, UserCheck, User } from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAdminAuth } from '@/contexts/AdminAuthContext';

const UserEditDialog = ({ user, onSave, children }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [status, setStatus] = useState(user.status);
    const [reason, setReason] = useState(user.reason || '');
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();
    const { getAuthHeaders } = useAdminAuth();

    const handleSave = async () => {
        setLoading(true);
        // TODO: Implement secure user update API
        const error = null;
        
        if (error) {
            toast({ title: "Error updating user", description: error.message, variant: "destructive" });
        } else {
            toast({ title: "User updated successfully!" });
            onSave();
            setIsOpen(false);
        }
        setLoading(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Edit User</DialogTitle>
                    <DialogDescription>HWID: {user.hwid}</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="status" className="text-right">Status</Label>
                        <Select value={status} onValueChange={setStatus}>
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select a status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="neutral">Neutral</SelectItem>
                                <SelectItem value="whitelisted">Whitelisted</SelectItem>
                                <SelectItem value="blacklisted">Blacklisted</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="reason" className="text-right">Reason</Label>
                        <Textarea id="reason" value={reason} onChange={(e) => setReason(e.target.value)} className="col-span-3" />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
                    <Button onClick={handleSave} disabled={loading}>
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Save Changes
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

const AdminUsers = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const { getAuthHeaders } = useAdminAuth();

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    // TODO: Implement secure get users API
    const data = []; 
    const error = null;
    if (error) {
      toast({ title: 'Error fetching users', description: error.message, variant: 'destructive' });
    } else {
      setUsers(data || []);
    }
    setLoading(false);
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const filteredUsers = useMemo(() => {
    return users.filter(user => user.hwid.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [users, searchTerm]);

  const getStatusBadge = (status) => {
    switch (status) {
        case 'blacklisted': return <span className="flex items-center gap-1 text-xs font-semibold text-red-500"><UserX className="w-3 h-3" /> Blacklisted</span>;
        case 'whitelisted': return <span className="flex items-center gap-1 text-xs font-semibold text-green-500"><UserCheck className="w-3 h-3" /> Whitelisted</span>;
        default: return <span className="flex items-center gap-1 text-xs font-semibold text-gray-500"><User className="w-3 h-3" /> Neutral</span>;
    }
  };

  return (
    <>
      <Helmet>
        <title>User Manager - 6FootScripts Admin</title>
      </Helmet>
      <PageWrapper title="User Manager" description="Manage all users, including whitelists and blacklists.">
        <Card>
          <CardHeader>
            <CardTitle>All Users</CardTitle>
            <CardDescription>Search, filter, and manage all registered users.</CardDescription>
            <div className="pt-4">
                <Input 
                    placeholder="Search by HWID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all">
              <TabsList>
                <TabsTrigger value="all">All ({filteredUsers.length})</TabsTrigger>
                <TabsTrigger value="whitelisted">Whitelisted ({filteredUsers.filter(u => u.status === 'whitelisted').length})</TabsTrigger>
                <TabsTrigger value="blacklisted">Blacklisted ({filteredUsers.filter(u => u.status === 'blacklisted').length})</TabsTrigger>
              </TabsList>
              {loading ? <div className="flex justify-center py-8"><Loader2 className="animate-spin h-8 w-8 text-primary" /></div> : (
                <>
                    <TabsContent value="all">{renderUserList(filteredUsers, fetchUsers, getStatusBadge)}</TabsContent>
                    <TabsContent value="whitelisted">{renderUserList(filteredUsers.filter(u => u.status === 'whitelisted'), fetchUsers, getStatusBadge)}</TabsContent>
                    <TabsContent value="blacklisted">{renderUserList(filteredUsers.filter(u => u.status === 'blacklisted'), fetchUsers, getStatusBadge)}</TabsContent>
                </>
              )}
            </Tabs>
          </CardContent>
        </Card>
      </PageWrapper>
    </>
  );
};

const renderUserList = (userList, onAction, getStatusBadge) => {
    if (userList.length === 0) {
        return <div className="text-center py-8 text-muted-foreground">No users found.</div>
    }
    return (
        <div className="rounded-md border mt-4">
            <div className="max-h-[60vh] overflow-y-auto">
            {userList.map(user => (
                <div key={user.id} className="flex justify-between items-center p-3 border-b last:border-b-0">
                <div>
                    <p className="font-mono text-sm">{user.hwid}</p>
                    <p className="text-xs text-muted-foreground">{user.reason || 'No reason provided'}</p>
                    <div className="mt-1">{getStatusBadge(user.status)}</div>
                </div>
                <UserEditDialog user={user} onSave={onAction}>
                    <Button variant="outline" size="icon"><Edit className="w-4 h-4" /></Button>
                </UserEditDialog>
                </div>
            ))}
            </div>
        </div>
    );
};

export default AdminUsers;
