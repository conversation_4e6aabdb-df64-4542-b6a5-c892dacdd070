
import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Info, Hourglass, CheckCircle2, XCircle, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';

const AdminRequests = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { getAuthHeaders } = useAdminAuth();

  const fetchRequests = useCallback(async () => {
    setLoading(true);
    try {
      const hwid = await getHwid();
      const result = await apiClient.getScriptRequests(hwid);
      setRequests(result.data || []);
    } catch (error) {
      console.error('Error fetching requests:', error);
      toast({ variant: 'destructive', title: 'Error', description: 'Failed to fetch script requests.' });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  const handleStatusChange = async (id, newStatus) => {
    const originalRequests = [...requests];
    const updatedRequests = requests.map(req => 
      req.id === id ? { ...req, status: newStatus } : req
    );
    setRequests(updatedRequests);

    try {
      const hwid = await getHwid();
      await apiClient.updateRequestStatus(hwid, id, newStatus);
      toast({
        title: "Status Updated!",
        description: `Request has been marked as ${newStatus}.`
      });
      fetchRequests(); 
    } catch (error) {
      setRequests(originalRequests);
      console.error('Error updating request status:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update request status.'
      });
    }
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 'Accepted':
        return {
          icon: <CheckCircle2 className="w-5 h-5 text-green-500" />,
          borderColor: 'border-l-green-500',
          textColor: 'text-green-500',
        };
      case 'Denied':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          borderColor: 'border-l-red-500',
          textColor: 'text-red-500',
        };
      default: 
        return {
          icon: <Hourglass className="w-5 h-5 text-yellow-500" />,
          borderColor: 'border-l-yellow-500',
          textColor: 'text-yellow-500',
        };
    }
  };

  return (
    <>
      <Helmet>
        <title>Script Requests - 6FootScripts Admin</title>
      </Helmet>
      <PageWrapper title="Script Requests" description="View and manage all user-submitted script requests.">
        {loading ? (
          <div className="text-center py-16">
            <Loader2 className="w-16 h-16 text-primary mx-auto mb-4 animate-spin" />
            <h3 className="text-2xl font-bold mb-2">Loading Requests...</h3>
            <p className="text-muted-foreground">Fetching the latest data from the secure API.</p>
          </div>
        ) : requests.length === 0 ? (
          <div className="text-center py-16 border rounded-lg">
            <Info className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-2">No Requests Yet</h3>
            <p className="text-muted-foreground">Looks like the request queue is empty.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {requests.map((request, index) => {
              const statusInfo = getStatusInfo(request.status);
              return (
                <motion.div
                  key={request.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className={`overflow-hidden border-l-4 ${statusInfo.borderColor}`}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{request.game_name}</CardTitle>
                          <CardDescription>Requested on {new Date(request.created_at).toLocaleDateString()}</CardDescription>
                        </div>
                        <div className={`flex items-center gap-2 font-semibold ${statusInfo.textColor}`}>
                          {statusInfo.icon}
                          <span>{request.status}</span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{request.script_description}</p>
                      {request.discord_username && (
                         <p className="text-xs text-muted-foreground mt-4">Requester: {request.discord_username}</p>
                      )}
                    </CardContent>
                    <CardFooter className="bg-accent/50 p-2 flex justify-end gap-2 border-t">
                        <Button size="sm" variant="ghost" className="text-green-500 hover:bg-green-500/10 hover:text-green-600" onClick={() => handleStatusChange(request.id, 'Accepted')}>Accept</Button>
                        <Button size="sm" variant="ghost" className="text-red-500 hover:bg-red-500/10 hover:text-red-600" onClick={() => handleStatusChange(request.id, 'Denied')}>Deny</Button>
                        <Button size="sm" variant="ghost" className="text-yellow-500 hover:bg-yellow-500/10 hover:text-yellow-600" onClick={() => handleStatusChange(request.id, 'Pending')}>Pend</Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}
      </PageWrapper>
    </>
  );
};

export default AdminRequests;
