import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { MessageCircle, ChevronRight, FilePlus, Code, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import AnimatedNumber from '@/components/AnimatedNumber';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';

const Home = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminCheckLoading, setAdminCheckLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const hwid = await getHwid();
        const result = await apiClient.checkAdminStatus(hwid);
        setIsAdmin(result.isAdmin);
      } catch (error) {
        console.error('Failed to check admin status:', error);
        setIsAdmin(false);
      } finally {
        setAdminCheckLoading(false);
      }
    };

    checkAdminStatus();
  }, []);

  const developers = [
    {
      name: '6Foot4Honda',
      title: 'Script Owner & Developer',
      description: 'Lead developer and owner of 6FootScripts. Responsible for script development, maintenance, and overall project direction.',
      gradientClass: 'gradient-text-honda',
      avatar: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/98f0f8020b8a290f454abc12077ff510.png',
      alt: 'Avatar for 6Foot4Honda',
      contactLink: 'https://discord.com/users/240070307094724608'
    },
    {
      name: 'Lunarbine',
      title: 'Script Developer & Website Owner',
      description: 'Script developer and website owner. Handles web development, script coding, and technical infrastructure management.',
      gradientClass: 'gradient-text-lunarbine',
      avatar: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/b37f650846d4e2af8503a5017ed63c94.png',
      alt: 'Avatar for Lunarbine',
      contactLink: 'https://discord.com/users/1012750340971581561'
    }
  ];

  const impactStats = [
    { value: 12, label: 'Total Scripts', suffix: '+' },
    { value: 10, label: 'Games Supported', suffix: '+' },
    { value: 1000, label: 'Happy Users', suffix: '+' },
    { value: '24/7', label: 'Support', suffix: '' }
  ];

  return (
    <>
      <Helmet>
        <title>Home - 6FootScripts</title>
        <meta name="description" content="Welcome to 6FootScripts. We provide high-quality, reliable scripts for your favorite games." />
      </Helmet>

      <div className="text-foreground overflow-x-hidden">
        <section className="relative py-24 md:py-32 px-4">
          <div className="absolute inset-0 -z-10 bg-primary/5 [mask-image:radial-gradient(ellipse_80%_50%_at_50%_-20%,rgba(120,119,198,0.3),rgba(255,255,255,0))]"></div>
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tighter">
                Elevate Your Gameplay with
                <span className="bg-gradient-to-br from-primary via-pink-500 to-orange-400 bg-clip-text text-transparent">
                  {' '}6FootScripts
                </span>
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                Discover high-quality, reliable, and efficient scripts to gain an edge in your favorite games.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mt-10 flex justify-center gap-4 flex-wrap"
            >
              <Button asChild size="lg">
                <Link to="/scripts">
                  <Code className="w-5 h-5 mr-2" />
                  Browse Scripts
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline">
                <Link to="/request-script">
                  <FilePlus className="w-5 h-5 mr-2" />
                  Request a Script
                </Link>
              </Button>
              {!adminCheckLoading && isAdmin && (
                <Button asChild size="lg" variant="secondary">
                  <Link to="/admin">
                    <Settings className="w-5 h-5 mr-2" />
                    Admin Panel
                  </Link>
                </Button>
              )}
            </motion.div>
          </div>
        </section>

        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">
                Meet the Developers
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                The masterminds behind the scripts, dedicated to quality and performance.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
              {developers.map((dev, index) => (
                <motion.div
                  key={dev.name}
                  initial={{ opacity: 0, y: 50, scale: 0.95 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="bg-card border rounded-2xl p-8 flex flex-col group hover:border-primary/50 transition-all duration-300"
                >
                  <div className="mb-6 text-center">
                    <img className="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-border group-hover:border-primary/50 transition-colors object-cover" alt={dev.alt} src={dev.avatar} />
                    <h3 className={`text-3xl font-bold ${dev.gradientClass}`}>{dev.name}</h3>
                    <p className="text-muted-foreground font-medium">{dev.title}</p>
                  </div>
                  <p className="text-foreground/90 mb-6 text-center flex-grow">{dev.description}</p>
                  <Button asChild className="mt-auto w-full" variant="outline">
                    <a href={dev.contactLink} target="_blank" rel="noopener noreferrer">
                      <MessageCircle className="w-5 h-5 mr-2" />
                      Contact {dev.name}
                    </a>
                  </Button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-20 px-4">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="p-8 md:p-12 text-center">
                <CardHeader>
                  <CardTitle className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">Our Impact</CardTitle>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                    We've helped thousands in the gaming community with our reliable and efficient scripts.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                    {impactStats.map((stat, index) => (
                      <div key={index}>
                        <div className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-pink-500 mb-2">
                          {typeof stat.value === 'number' ? <AnimatedNumber value={stat.value} /> : stat.value}
                          {stat.suffix}
                        </div>
                        <div className="text-muted-foreground">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>

        <section className="py-20 px-4">
          <div className="max-w-3xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">
                Have an Idea for a Script?
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                We are always looking for new ideas. If you have a script in mind that you'd like to see, let us know!
              </p>
              <Button asChild size="lg">
                <Link to="/request-script">
                  <FilePlus className="w-5 h-5 mr-2" />
                  Request a Script
                  <ChevronRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;