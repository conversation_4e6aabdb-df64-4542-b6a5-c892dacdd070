
import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';
import GlobalLoader from '@/components/GlobalLoader';

const AdminAccessGuard = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuthorization = async () => {
      if (location.pathname === '/admin/login') {
        setIsAuthorized(true);
        return;
      }

      try {
        const hwid = await getHwid();
        if (!hwid) {
          setIsAuthorized(false);
          return;
        }

        const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
        if (isAuthenticated) {
          setIsAuthorized(true);
          return;
        }

        const result = await apiClient.checkAdminStatus(hwid);
        setIsAuthorized(result.isAdmin);
      } catch (error) {
        console.error('Admin check failed:', error);
        setIsAuthorized(false);
      }
    };

    checkAuthorization();
  }, [location.pathname]);

  if (isAuthorized === null) {
    return <GlobalLoader />;
  }

  if (!isAuthorized) {
    return <Navigate to="/" replace />;
  }

  const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
  if (!isAuthenticated && location.pathname !== '/admin/login') {
    return <Navigate to="/admin/login" replace />;
  }

  return children;
};

export default AdminAccessGuard;
