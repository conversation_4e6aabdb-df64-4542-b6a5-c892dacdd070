const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  throw new Error(`Missing Supabase environment variables.`);
}

let supabase;
try {
  supabase = createClient(supabaseUrl, supabaseKey);
  console.log('Supabase client created successfully');
} catch (error) {
  console.error('Failed to create Supabase client:', error);
  throw error;
}

const isValidHwid = (hwid) => {
  return hwid && typeof hwid === 'string' && hwid.length > 0;
};

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

exports.handler = async (event) => {
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {

    const { action, params } = JSON.parse(event.body || '{}');

    switch (action) {
      case 'getScripts':
        return await getScripts();

      case 'rateScript':
        return await rateScript(params);

      case 'getUserRatings':
        return await getUserRatings(params);

      case 'submitScriptRequest':
        return await submitScriptRequest(params);

      case 'trackUser':
        return await trackUser(params);

      case 'checkUserStatus':
        return await checkUserStatus(params);

      case 'checkAdminStatus':
        return await checkAdminStatus(params);

      case 'adminLogin':
        return await adminLogin(params);

      case 'getAdminKeys':
        return await getAdminKeys(params);

      case 'getScriptRequests':
        return await getScriptRequests(params);

      case 'updateRequestStatus':
        return await updateRequestStatus(params);

      case 'createScript':
        return await createScript(params);

      case 'updateScript':
        return await updateScript(params);

      case 'deleteScript':
        return await deleteScript(params);

      case 'getAdminStats':
        return await getAdminStats(params);

      case 'test':
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            message: 'API is working!',
            timestamp: new Date().toISOString(),
            env: {
              hasUrl: !!supabaseUrl,
              hasKey: !!supabaseKey,
              nodeEnv: process.env.NODE_ENV
            }
          })
        };

      default:
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ error: `Invalid action: ${action}` })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    console.error('Error stack:', error.stack);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      })
    };
  }
};

async function getScripts() {
  try {
    const { data, error } = await supabase
      .from('scripts')
      .select(`*, script_ratings ( rating )`);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function rateScript({ scriptId, rating, hwid }) {
  try {
    if (!isValidHwid(hwid) || !scriptId || rating === undefined) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .upsert({
        script_id: scriptId,
        hwid: hwid,
        rating: rating
      }, {
        onConflict: 'script_id,hwid'
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function getUserRatings({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .select('script_id, rating')
      .eq('hwid', hwid);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function submitScriptRequest({ gameName, gameLink, scriptDescription, discordUsername, hwid }) {
  try {
    if (!gameName || !scriptDescription || !hwid) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required fields' })
      };
    }

    // Create a unique ID by combining HWID with timestamp to avoid conflicts
    const uniqueId = `${hwid}-${Date.now()}`;

    const { data, error } = await supabase
      .from('script_requests')
      .insert({
        id: uniqueId, // Use HWID + timestamp as ID for spam tracking
        game_name: gameName,
        game_link: gameLink,
        script_description: scriptDescription,
        discord_username: discordUsername || null,
        status: 'Pending',
      })
      .select()
      .single();

    if (error) throw error;

    const webhookUrl = process.env.DISCORD_WEBHOOK_URL;
    if (webhookUrl) {
      try {
        const embed = {
          title: 'New Script Request',
          color: 8535234,
          fields: [
            { name: 'Game Name', value: gameName, inline: true },
            { name: 'Discord Username', value: discordUsername || 'Not Provided', inline: true },
            { name: 'Status', value: '🕒 Pending', inline: true },
            { name: 'Game Link', value: `[Click Here](${gameLink})` },
            { name: 'Script Description', value: scriptDescription },
            { name: 'Request ID', value: `\`${data.id}\``, inline: false}
          ],
          footer: { text: '6FootScripts Request System' },
          timestamp: new Date().toISOString(),
        };

        const payload = { embeds: [embed] };

        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        console.log('Discord notification sent successfully');
      } catch (discordError) {
        console.error('Failed to send Discord notification:', discordError);
      }
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function trackUser({ hwid, path }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { error: upsertError } = await supabase
      .from('users')
      .upsert({
        hwid: hwid,
        status: 'neutral'
      }, {
        onConflict: 'hwid',
        ignoreDuplicates: true
      });

    if (upsertError) {
      console.error("Error ensuring user exists:", upsertError);
    }

    const { data, error } = await supabase
      .from('page_visits')
      .insert({
        hwid,
        path
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function checkUserStatus({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data, error } = await supabase
      .from('users')
      .select('status')
      .eq('hwid', hwid)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Supabase error (not PGRST116):', error);
      throw error;
    }

    const isBlacklisted = data?.status === 'blacklisted';

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isBlacklisted })
    };
  } catch (error) {
    console.error('checkUserStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function checkAdminStatus({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data, error } = await supabase
      .from('admin_keys')
      .select('id, name')
      .eq('hwid', hwid)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Supabase error (not PGRST116):', error);
      throw error;
    }

    const isAdmin = !!data;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isAdmin })
    };
  } catch (error) {
    console.error('checkAdminStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function adminLogin({ accessKey, hwid }) {
  try {
    if (!accessKey || !isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing access key or invalid HWID' })
      };
    }

    const { data: keyData, error: keyError } = await supabase
      .from('admin_keys')
      .select('id, name, hwid')
      .eq('access_key', accessKey)
      .single();

    if (keyError || !keyData) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid access key' })
      };
    }

    if (!keyData.hwid) {
      const { error: updateError } = await supabase
        .from('admin_keys')
        .update({
          hwid: hwid,
          assigned_at: new Date().toISOString()
        })
        .eq('id', keyData.id);

      if (updateError) {
        console.error('Failed to assign key to HWID:', updateError);
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Failed to assign access key' })
        };
      }

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Access key assigned successfully'
        })
      };
    }

    if (keyData.hwid === hwid) {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Login successful'
        })
      };
    } else {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Access key is assigned to a different device' })
      };
    }

  } catch (error) {
    console.error('adminLogin error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function getAdminKeys({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('admin_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('getAdminKeys error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function getScriptRequests({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('getScriptRequests error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function updateRequestStatus({ hwid, requestId, status }) {
  try {
    if (!isValidHwid(hwid) || !requestId || !status) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .update({ status })
      .eq('id', requestId)
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('updateRequestStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function createScript({ hwid, scriptData }) {
  try {
    if (!isValidHwid(hwid) || !scriptData) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('scripts')
      .insert([scriptData])
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('createScript error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function updateScript({ hwid, scriptId, scriptData }) {
  try {
    if (!isValidHwid(hwid) || !scriptId || !scriptData) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('scripts')
      .update(scriptData)
      .eq('id', scriptId)
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('updateScript error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function deleteScript({ hwid, scriptId }) {
  try {
    if (!isValidHwid(hwid) || !scriptId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { error } = await supabase
      .from('scripts')
      .delete()
      .eq('id', scriptId);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ success: true })
    };
  } catch (error) {
    console.error('deleteScript error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function getAdminStats({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const [requestsResult, keysResult, visitsResult, scriptsResult] = await Promise.all([
      supabase.from('script_requests').select('id, created_at, status'),
      supabase.from('admin_keys').select('id'),
      supabase.from('page_visits').select('id, created_at'),
      supabase.from('scripts').select('id, created_at')
    ]);

    if (requestsResult.error) throw requestsResult.error;
    if (keysResult.error) throw keysResult.error;
    if (visitsResult.error) throw visitsResult.error;
    if (scriptsResult.error) throw scriptsResult.error;

    const requestsData = requestsResult.data || [];
    const keysData = keysResult.data || [];
    const visitsData = visitsResult.data || [];
    const scriptsData = scriptsResult.data || [];

    const monthlyData = requestsData.reduce((acc, req) => {
      const month = new Date(req.created_at).toLocaleString('default', { month: 'short', year: 'numeric' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {});
    const requestsChartData = Object.entries(monthlyData).map(([name, requests]) => ({ name, requests }));

    const statusData = requestsData.reduce((acc, req) => {
      acc[req.status] = (acc[req.status] || 0) + 1;
      return acc;
    }, {});
    const requestsStatusData = Object.entries(statusData).map(([name, value]) => ({ name, value }));

    const uniqueVisitors = new Set(visitsData.map(visit => visit.hwid || 'anonymous')).size;

    const stats = {
      requests: requestsData.length,
      keys: keysData.length,
      visits: uniqueVisitors,
      scripts: scriptsData.length,
      requestsChartData,
      requestsStatusData
    };

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ stats })
    };
  } catch (error) {
    console.error('getAdminStats error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

